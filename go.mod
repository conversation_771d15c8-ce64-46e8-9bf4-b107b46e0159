module cmdb-platform

go 1.21

require (
	github.com/gin-gonic/gin v1.9.1
	github.com/gin-contrib/cors v1.4.0
	github.com/gin-contrib/sessions v0.0.5
	github.com/swaggo/gin-swagger v1.6.0
	github.com/swaggo/files v1.0.1
	github.com/swaggo/swag v1.16.1
	
	// Database
	gorm.io/gorm v1.25.4
	gorm.io/driver/postgres v1.5.2
	github.com/go-redis/redis/v8 v8.11.5
	
	// Cloud SDKs
	github.com/aliyun/alibaba-cloud-sdk-go v1.62.156
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.490
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm v1.0.490
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc v1.0.490
	github.com/aws/aws-sdk-go v1.44.327
	
	// Authentication & Security
	github.com/golang-jwt/jwt/v5 v5.0.0
	github.com/casbin/casbin/v2 v2.77.2
	github.com/casbin/gorm-adapter/v3 v3.18.0
	golang.org/x/crypto v0.12.0
	
	// Configuration & Logging
	github.com/spf13/viper v1.16.0
	github.com/sirupsen/logrus v1.9.3
	github.com/natefinch/lumberjack v2.0.0+incompatible
	
	// Utilities
	github.com/google/uuid v1.3.0
	github.com/robfig/cron/v3 v3.0.1
	github.com/gorilla/websocket v1.5.0
	github.com/go-playground/validator/v10 v10.15.1
	github.com/json-iterator/go v1.1.12
	
	// Testing
	github.com/stretchr/testify v1.8.4
	github.com/golang/mock v1.6.0
)
