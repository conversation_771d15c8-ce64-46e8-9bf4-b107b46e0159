package auth

import (
	"errors"
	"time"

	"cmdb-platform/internal/config"
	"cmdb-platform/internal/model"
	
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// Claims JWT声明
type Claims struct {
	UserID   uuid.UUID         `json:"user_id"`
	Username string            `json:"username"`
	Email    string            `json:"email"`
	Role     model.UserRole    `json:"role"`
	TenantID uuid.UUID         `json:"tenant_id"`
	jwt.RegisteredClaims
}

// JWTManager JWT管理器
type JWTManager struct {
	secretKey       string
	expireDuration  time.Duration
	refreshDuration time.Duration
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(cfg *config.JWTConfig) *JWTManager {
	return &JWTManager{
		secretKey:       cfg.Secret,
		expireDuration:  time.Duration(cfg.ExpireHours) * time.Hour,
		refreshDuration: time.Duration(cfg.RefreshExpireHours) * time.Hour,
	}
}

// GenerateToken 生成访问令牌
func (j *JWTManager) GenerateToken(user *model.User, tenantID uuid.UUID) (string, error) {
	now := time.Now()
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		TenantID: tenantID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expireDuration)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "cmdb-platform",
			Subject:   user.ID.String(),
			ID:        uuid.New().String(),
		},
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// GenerateRefreshToken 生成刷新令牌
func (j *JWTManager) GenerateRefreshToken(user *model.User, tenantID uuid.UUID) (string, error) {
	now := time.Now()
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		TenantID: tenantID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshDuration)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "cmdb-platform",
			Subject:   user.ID.String(),
			ID:        uuid.New().String(),
		},
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// ValidateToken 验证令牌
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return []byte(j.secretKey), nil
	})
	
	if err != nil {
		return nil, err
	}
	
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}
	
	return nil, errors.New("invalid token")
}

// RefreshToken 刷新令牌
func (j *JWTManager) RefreshToken(refreshTokenString string) (string, string, error) {
	claims, err := j.ValidateToken(refreshTokenString)
	if err != nil {
		return "", "", err
	}
	
	// 检查是否为刷新令牌（通过过期时间判断）
	if claims.ExpiresAt.Time.Sub(claims.IssuedAt.Time) < j.refreshDuration {
		return "", "", errors.New("not a refresh token")
	}
	
	// 创建新的用户对象
	user := &model.User{
		BaseModel: model.BaseModel{
			ID: claims.UserID,
		},
		Username: claims.Username,
		Email:    claims.Email,
		Role:     claims.Role,
	}
	
	// 生成新的访问令牌和刷新令牌
	accessToken, err := j.GenerateToken(user, claims.TenantID)
	if err != nil {
		return "", "", err
	}
	
	refreshToken, err := j.GenerateRefreshToken(user, claims.TenantID)
	if err != nil {
		return "", "", err
	}
	
	return accessToken, refreshToken, nil
}

// GetClaimsFromToken 从令牌中获取声明（不验证过期时间）
func (j *JWTManager) GetClaimsFromToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return []byte(j.secretKey), nil
	}, jwt.WithoutClaimsValidation())
	
	if err != nil {
		return nil, err
	}
	
	if claims, ok := token.Claims.(*Claims); ok {
		return claims, nil
	}
	
	return nil, errors.New("invalid token claims")
}

// IsTokenExpired 检查令牌是否过期
func (j *JWTManager) IsTokenExpired(tokenString string) bool {
	claims, err := j.GetClaimsFromToken(tokenString)
	if err != nil {
		return true
	}
	
	return claims.ExpiresAt.Time.Before(time.Now())
}

// GetTokenTTL 获取令牌剩余时间
func (j *JWTManager) GetTokenTTL(tokenString string) (time.Duration, error) {
	claims, err := j.GetClaimsFromToken(tokenString)
	if err != nil {
		return 0, err
	}
	
	ttl := claims.ExpiresAt.Time.Sub(time.Now())
	if ttl < 0 {
		return 0, nil
	}
	
	return ttl, nil
}
