package aliyun

import (
	"context"
	"fmt"

	"cmdb-platform/pkg/cloud"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/rds"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/slb"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/vpc"
)

// Client 阿里云客户端
type Client struct {
	accessKeyID     string
	accessKeySecret string
	region          string
	ecsClient       *ecs.Client
	vpcClient       *vpc.Client
	slbClient       *slb.Client
	rdsClient       *rds.Client
}

// NewClient 创建阿里云客户端
func NewClient(accessKeyID, accessKeySecret, region string) (*Client, error) {
	client := &Client{
		accessKeyID:     accessKeyID,
		accessKeySecret: accessKeySecret,
		region:          region,
	}

	if err := client.initClients(); err != nil {
		return nil, fmt.Errorf("failed to initialize clients: %w", err)
	}

	return client, nil
}

// initClients 初始化各服务客户端
func (c *Client) initClients() error {
	var err error

	// 初始化ECS客户端
	c.ecsClient, err = ecs.NewClientWithAccessKey(c.region, c.accessKeyID, c.accessKeySecret)
	if err != nil {
		return fmt.Errorf("failed to create ECS client: %w", err)
	}

	// 初始化VPC客户端
	c.vpcClient, err = vpc.NewClientWithAccessKey(c.region, c.accessKeyID, c.accessKeySecret)
	if err != nil {
		return fmt.Errorf("failed to create VPC client: %w", err)
	}

	// 初始化SLB客户端
	c.slbClient, err = slb.NewClientWithAccessKey(c.region, c.accessKeyID, c.accessKeySecret)
	if err != nil {
		return fmt.Errorf("failed to create SLB client: %w", err)
	}

	// 初始化RDS客户端
	c.rdsClient, err = rds.NewClientWithAccessKey(c.region, c.accessKeyID, c.accessKeySecret)
	if err != nil {
		return fmt.Errorf("failed to create RDS client: %w", err)
	}

	return nil
}

// GetProviderName 获取云服务商名称
func (c *Client) GetProviderName() string {
	return "aliyun"
}

// GetRegions 获取地域列表
func (c *Client) GetRegions(ctx context.Context) ([]cloud.Region, error) {
	request := ecs.CreateDescribeRegionsRequest()
	request.Scheme = "https"

	response, err := c.ecsClient.DescribeRegions(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe regions: %w", err)
	}

	var regions []cloud.Region
	for _, region := range response.Regions.Region {
		regions = append(regions, cloud.Region{
			ID:          region.RegionId,
			Name:        region.LocalName,
			Description: region.RegionId,
			Status:      "available",
		})
	}

	return regions, nil
}

// GetZones 获取可用区列表
func (c *Client) GetZones(ctx context.Context, region string) ([]cloud.Zone, error) {
	request := ecs.CreateDescribeZonesRequest()
	request.Scheme = "https"
	request.RegionId = region

	response, err := c.ecsClient.DescribeZones(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe zones: %w", err)
	}

	var zones []cloud.Zone
	for _, zone := range response.Zones.Zone {
		zones = append(zones, cloud.Zone{
			ID:          zone.ZoneId,
			Name:        zone.LocalName,
			Description: zone.ZoneId,
			Status:      "available",
			RegionID:    region,
		})
	}

	return zones, nil
}

// ListInstances 列出ECS实例
func (c *Client) ListInstances(ctx context.Context, region string) ([]cloud.Instance, error) {
	request := ecs.CreateDescribeInstancesRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.PageSize = "100"

	var instances []cloud.Instance
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.ecsClient.DescribeInstances(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe instances: %w", err)
		}

		for _, instance := range response.Instances.Instance {
			// 获取实例详细信息
			cloudInstance := c.convertECSInstance(&instance)
			instances = append(instances, cloudInstance)
		}

		// 检查是否还有更多页
		if len(response.Instances.Instance) < 100 {
			break
		}
		pageNumber++
	}

	return instances, nil
}

// GetInstance 获取单个ECS实例
func (c *Client) GetInstance(ctx context.Context, region, instanceID string) (*cloud.Instance, error) {
	request := ecs.CreateDescribeInstancesRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.InstanceIds = fmt.Sprintf("[\"%s\"]", instanceID)

	response, err := c.ecsClient.DescribeInstances(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe instance: %w", err)
	}

	if len(response.Instances.Instance) == 0 {
		return nil, fmt.Errorf("instance %s not found", instanceID)
	}

	instance := c.convertECSInstance(&response.Instances.Instance[0])
	return &instance, nil
}

// convertECSInstance 转换ECS实例格式
func (c *Client) convertECSInstance(instance *ecs.Instance) cloud.Instance {
	// 获取公网IP
	var publicIP string
	if len(instance.PublicIpAddress.IpAddress) > 0 {
		publicIP = instance.PublicIpAddress.IpAddress[0]
	}
	if publicIP == "" && len(instance.EipAddress.IpAddress) > 0 {
		publicIP = instance.EipAddress.IpAddress
	}

	// 获取私网IP
	var privateIP string
	if len(instance.InnerIpAddress.IpAddress) > 0 {
		privateIP = instance.InnerIpAddress.IpAddress[0]
	}
	if privateIP == "" && len(instance.VpcAttributes.PrivateIpAddress.IpAddress) > 0 {
		privateIP = instance.VpcAttributes.PrivateIpAddress.IpAddress[0]
	}

	// 获取安全组ID列表
	var securityGroupIDs []string
	for _, sg := range instance.SecurityGroupIds.SecurityGroupId {
		securityGroupIDs = append(securityGroupIDs, sg)
	}

	// 转换标签
	tags := make(map[string]string)
	for _, tag := range instance.Tags.Tag {
		tags[tag.TagKey] = tag.TagValue
	}

	return cloud.Instance{
		ID:               instance.InstanceId,
		Name:             instance.InstanceName,
		Status:           instance.Status,
		InstanceType:     instance.InstanceType,
		CPU:              instance.Cpu,
		Memory:           instance.Memory,
		OSType:           instance.OSType,
		OSName:           instance.OSName,
		PublicIP:         publicIP,
		PrivateIP:        privateIP,
		VpcID:            instance.VpcAttributes.VpcId,
		SubnetID:         instance.VpcAttributes.VSwitchId,
		SecurityGroupIDs: securityGroupIDs,
		Tags:             tags,
		CreatedAt:        instance.CreationTime,
		Region:           instance.RegionId,
		Zone:             instance.ZoneId,
	}
}

// ListVPCs 列出VPC
func (c *Client) ListVPCs(ctx context.Context, region string) ([]cloud.VPC, error) {
	request := vpc.CreateDescribeVpcsRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.PageSize = "50"

	var vpcs []cloud.VPC
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.vpcClient.DescribeVpcs(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe VPCs: %w", err)
		}

		for _, vpcItem := range response.Vpcs.Vpc {
			cloudVPC := c.convertVPC(&vpcItem)
			vpcs = append(vpcs, cloudVPC)
		}

		// 检查是否还有更多页
		if len(response.Vpcs.Vpc) < 50 {
			break
		}
		pageNumber++
	}

	return vpcs, nil
}

// GetVPC 获取单个VPC
func (c *Client) GetVPC(ctx context.Context, region, vpcID string) (*cloud.VPC, error) {
	request := vpc.CreateDescribeVpcsRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.VpcId = vpcID

	response, err := c.vpcClient.DescribeVpcs(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe VPC: %w", err)
	}

	if len(response.Vpcs.Vpc) == 0 {
		return nil, fmt.Errorf("VPC %s not found", vpcID)
	}

	cloudVPC := c.convertVPC(&response.Vpcs.Vpc[0])
	return &cloudVPC, nil
}

// convertVPC 转换VPC格式
func (c *Client) convertVPC(vpcItem *vpc.Vpc) cloud.VPC {
	// 转换标签
	tags := make(map[string]string)
	for _, tag := range vpcItem.Tags.Tag {
		tags[tag.Key] = tag.Value
	}

	return cloud.VPC{
		ID:          vpcItem.VpcId,
		Name:        vpcItem.VpcName,
		Status:      vpcItem.Status,
		CidrBlock:   vpcItem.CidrBlock,
		Description: vpcItem.Description,
		Tags:        tags,
		CreatedAt:   vpcItem.CreationTime,
		Region:      vpcItem.RegionId,
	}
}

// ListSubnets 列出子网
func (c *Client) ListSubnets(ctx context.Context, region, vpcID string) ([]cloud.Subnet, error) {
	request := vpc.CreateDescribeVSwitchesRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.VpcId = vpcID
	request.PageSize = "50"

	var subnets []cloud.Subnet
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.vpcClient.DescribeVSwitches(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe VSwitches: %w", err)
		}

		for _, vswitch := range response.VSwitches.VSwitch {
			cloudSubnet := c.convertVSwitch(&vswitch)
			subnets = append(subnets, cloudSubnet)
		}

		// 检查是否还有更多页
		if len(response.VSwitches.VSwitch) < 50 {
			break
		}
		pageNumber++
	}

	return subnets, nil
}

// GetSubnet 获取单个子网
func (c *Client) GetSubnet(ctx context.Context, region, subnetID string) (*cloud.Subnet, error) {
	request := vpc.CreateDescribeVSwitchesRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.VSwitchId = subnetID

	response, err := c.vpcClient.DescribeVSwitches(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe VSwitch: %w", err)
	}

	if len(response.VSwitches.VSwitch) == 0 {
		return nil, fmt.Errorf("VSwitch %s not found", subnetID)
	}

	cloudSubnet := c.convertVSwitch(&response.VSwitches.VSwitch[0])
	return &cloudSubnet, nil
}

// convertVSwitch 转换VSwitch格式
func (c *Client) convertVSwitch(vswitch *vpc.VSwitch) cloud.Subnet {
	// 转换标签
	tags := make(map[string]string)
	for _, tag := range vswitch.Tags.Tag {
		tags[tag.Key] = tag.Value
	}

	return cloud.Subnet{
		ID:               vswitch.VSwitchId,
		Name:             vswitch.VSwitchName,
		Status:           vswitch.Status,
		VpcID:            vswitch.VpcId,
		CidrBlock:        vswitch.CidrBlock,
		AvailableIPCount: int(vswitch.AvailableIpAddressCount),
		Description:      vswitch.Description,
		Tags:             tags,
		CreatedAt:        vswitch.CreationTime,
		Region:           vswitch.RegionId,
		Zone:             vswitch.ZoneId,
	}
}

// ListLoadBalancers 列出负载均衡器
func (c *Client) ListLoadBalancers(ctx context.Context, region string) ([]cloud.LoadBalancer, error) {
	request := slb.CreateDescribeLoadBalancersRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.PageSize = "50"

	var loadBalancers []cloud.LoadBalancer
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.slbClient.DescribeLoadBalancers(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe load balancers: %w", err)
		}

		for _, lb := range response.LoadBalancers.LoadBalancer {
			cloudLB := c.convertLoadBalancer(&lb)
			loadBalancers = append(loadBalancers, cloudLB)
		}

		// 检查是否还有更多页
		if len(response.LoadBalancers.LoadBalancer) < 50 {
			break
		}
		pageNumber++
	}

	return loadBalancers, nil
}

// GetLoadBalancer 获取单个负载均衡器
func (c *Client) GetLoadBalancer(ctx context.Context, region, lbID string) (*cloud.LoadBalancer, error) {
	request := slb.CreateDescribeLoadBalancersRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.LoadBalancerId = lbID

	response, err := c.slbClient.DescribeLoadBalancers(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe load balancer: %w", err)
	}

	if len(response.LoadBalancers.LoadBalancer) == 0 {
		return nil, fmt.Errorf("load balancer %s not found", lbID)
	}

	cloudLB := c.convertLoadBalancer(&response.LoadBalancers.LoadBalancer[0])
	return &cloudLB, nil
}

// convertLoadBalancer 转换负载均衡器格式
func (c *Client) convertLoadBalancer(lb *slb.LoadBalancer) cloud.LoadBalancer {
	// 转换标签
	tags := make(map[string]string)
	for _, tag := range lb.Tags.Tag {
		tags[tag.TagKey] = tag.TagValue
	}

	// 获取监听器信息（这里简化处理，实际应该调用DescribeLoadBalancerListeners）
	var listeners []cloud.LBListener

	return cloud.LoadBalancer{
		ID:        lb.LoadBalancerId,
		Name:      lb.LoadBalancerName,
		Status:    lb.LoadBalancerStatus,
		Type:      lb.LoadBalancerSpec,
		VpcID:     lb.VpcId,
		SubnetIDs: []string{lb.VSwitchId},
		PublicIP:  lb.Address,
		PrivateIP: "",
		Listeners: listeners,
		Tags:      tags,
		CreatedAt: lb.CreateTime,
		Region:    lb.RegionId,
	}
}

// ListDisks 列出云盘
func (c *Client) ListDisks(ctx context.Context, region string) ([]cloud.Disk, error) {
	request := ecs.CreateDescribeDisksRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.PageSize = "100"

	var disks []cloud.Disk
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.ecsClient.DescribeDisks(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe disks: %w", err)
		}

		for _, disk := range response.Disks.Disk {
			cloudDisk := c.convertDisk(&disk)
			disks = append(disks, cloudDisk)
		}

		// 检查是否还有更多页
		if len(response.Disks.Disk) < 100 {
			break
		}
		pageNumber++
	}

	return disks, nil
}

// GetDisk 获取单个云盘
func (c *Client) GetDisk(ctx context.Context, region, diskID string) (*cloud.Disk, error) {
	request := ecs.CreateDescribeDisksRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.DiskIds = fmt.Sprintf("[\"%s\"]", diskID)

	response, err := c.ecsClient.DescribeDisks(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe disk: %w", err)
	}

	if len(response.Disks.Disk) == 0 {
		return nil, fmt.Errorf("disk %s not found", diskID)
	}

	cloudDisk := c.convertDisk(&response.Disks.Disk[0])
	return &cloudDisk, nil
}

// convertDisk 转换云盘格式
func (c *Client) convertDisk(disk *ecs.Disk) cloud.Disk {
	// 转换标签
	tags := make(map[string]string)
	for _, tag := range disk.Tags.Tag {
		tags[tag.TagKey] = tag.TagValue
	}

	return cloud.Disk{
		ID:         disk.DiskId,
		Name:       disk.DiskName,
		Status:     disk.Status,
		Type:       disk.Type,
		Size:       disk.Size,
		InstanceID: disk.InstanceId,
		Device:     disk.Device,
		Encrypted:  disk.Encrypted,
		Tags:       tags,
		CreatedAt:  disk.CreationTime,
		Region:     disk.RegionId,
		Zone:       disk.ZoneId,
	}
}

// ListDatabases 列出数据库实例
func (c *Client) ListDatabases(ctx context.Context, region string) ([]cloud.Database, error) {
	request := rds.CreateDescribeDBInstancesRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.PageSize = "30"

	var databases []cloud.Database
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.rdsClient.DescribeDBInstances(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe DB instances: %w", err)
		}

		for _, dbInstance := range response.Items.DBInstance {
			cloudDB := c.convertDatabase(&dbInstance)
			databases = append(databases, cloudDB)
		}

		// 检查是否还有更多页
		if len(response.Items.DBInstance) < 30 {
			break
		}
		pageNumber++
	}

	return databases, nil
}

// GetDatabase 获取单个数据库实例
func (c *Client) GetDatabase(ctx context.Context, region, dbID string) (*cloud.Database, error) {
	request := rds.CreateDescribeDBInstancesRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.DBInstanceId = dbID

	response, err := c.rdsClient.DescribeDBInstances(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe DB instance: %w", err)
	}

	if len(response.Items.DBInstance) == 0 {
		return nil, fmt.Errorf("DB instance %s not found", dbID)
	}

	cloudDB := c.convertDatabase(&response.Items.DBInstance[0])
	return &cloudDB, nil
}

// convertDatabase 转换数据库格式
func (c *Client) convertDatabase(dbInstance *rds.DBInstance) cloud.Database {
	// 转换标签
	tags := make(map[string]string)
	for _, tag := range dbInstance.Tags.Tag {
		tags[tag.TagKey] = tag.TagValue
	}

	return cloud.Database{
		ID:           dbInstance.DBInstanceId,
		Name:         dbInstance.DBInstanceDescription,
		Status:       dbInstance.DBInstanceStatus,
		Engine:       dbInstance.Engine,
		Version:      dbInstance.EngineVersion,
		InstanceType: dbInstance.DBInstanceClass,
		CPU:          dbInstance.DBInstanceCPU,
		Memory:       int(dbInstance.DBInstanceMemory),
		Storage:      dbInstance.DBInstanceStorage,
		VpcID:        dbInstance.VpcId,
		SubnetID:     dbInstance.VSwitchId,
		PublicIP:     "",
		PrivateIP:    "",
		Port:         dbInstance.Port,
		Tags:         tags,
		CreatedAt:    dbInstance.CreationTime,
		Region:       dbInstance.RegionId,
		Zone:         dbInstance.ZoneId,
	}
}

// ListSecurityGroups 列出安全组
func (c *Client) ListSecurityGroups(ctx context.Context, region string) ([]cloud.SecurityGroup, error) {
	request := ecs.CreateDescribeSecurityGroupsRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.PageSize = "50"

	var securityGroups []cloud.SecurityGroup
	pageNumber := 1

	for {
		request.PageNumber = fmt.Sprintf("%d", pageNumber)
		response, err := c.ecsClient.DescribeSecurityGroups(request)
		if err != nil {
			return nil, fmt.Errorf("failed to describe security groups: %w", err)
		}

		for _, sg := range response.SecurityGroups.SecurityGroup {
			cloudSG := c.convertSecurityGroup(&sg)
			securityGroups = append(securityGroups, cloudSG)
		}

		// 检查是否还有更多页
		if len(response.SecurityGroups.SecurityGroup) < 50 {
			break
		}
		pageNumber++
	}

	return securityGroups, nil
}

// GetSecurityGroup 获取单个安全组
func (c *Client) GetSecurityGroup(ctx context.Context, region, sgID string) (*cloud.SecurityGroup, error) {
	request := ecs.CreateDescribeSecurityGroupsRequest()
	request.Scheme = "https"
	request.RegionId = region
	request.SecurityGroupId = sgID

	response, err := c.ecsClient.DescribeSecurityGroups(request)
	if err != nil {
		return nil, fmt.Errorf("failed to describe security group: %w", err)
	}

	if len(response.SecurityGroups.SecurityGroup) == 0 {
		return nil, fmt.Errorf("security group %s not found", sgID)
	}

	cloudSG := c.convertSecurityGroup(&response.SecurityGroups.SecurityGroup[0])
	return &cloudSG, nil
}

// convertSecurityGroup 转换安全组格式
func (c *Client) convertSecurityGroup(sg *ecs.SecurityGroup) cloud.SecurityGroup {
	// 转换标签
	tags := make(map[string]string)
	for _, tag := range sg.Tags.Tag {
		tags[tag.TagKey] = tag.TagValue
	}

	// 获取安全组规则（这里简化处理，实际应该调用DescribeSecurityGroupAttribute）
	var rules []cloud.SecurityRule

	return cloud.SecurityGroup{
		ID:          sg.SecurityGroupId,
		Name:        sg.SecurityGroupName,
		Description: sg.Description,
		VpcID:       sg.VpcId,
		Rules:       rules,
		Tags:        tags,
		CreatedAt:   sg.CreationTime,
		Region:      sg.RegionId,
	}
}
