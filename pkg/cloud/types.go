package cloud

import (
	"time"

	"cmdb-platform/internal/model"
	"github.com/google/uuid"
)

// SyncResult 同步结果
type SyncResult struct {
	CloudAccountID uuid.UUID             `json:"cloud_account_id"`
	Provider       model.CloudProvider   `json:"provider"`
	StartTime      time.Time             `json:"start_time"`
	EndTime        time.Time             `json:"end_time"`
	Duration       time.Duration         `json:"duration"`
	TotalCount     int                   `json:"total_count"`
	SuccessCount   int                   `json:"success_count"`
	FailureCount   int                   `json:"failure_count"`
	Resources      []model.Resource      `json:"resources"`
	Errors         []error               `json:"errors"`
}

// ResourceConverter 资源转换器
type ResourceConverter struct {
	account *model.CloudAccount
}

// NewResourceConverter 创建资源转换器
func NewResourceConverter(account *model.CloudAccount) *ResourceConverter {
	return &ResourceConverter{
		account: account,
	}
}

// ConvertInstanceToResource 转换云服务器实例为资源
func (c *ResourceConverter) ConvertInstanceToResource(instance *Instance) (*model.Resource, error) {
	now := time.Now()
	
	// 构建属性
	properties := map[string]interface{}{
		"instance_type":      instance.InstanceType,
		"cpu":               instance.CPU,
		"memory":            instance.Memory,
		"os_type":           instance.OSType,
		"os_name":           instance.OSName,
		"public_ip":         instance.PublicIP,
		"private_ip":        instance.PrivateIP,
		"vpc_id":            instance.VpcID,
		"subnet_id":         instance.SubnetID,
		"security_group_ids": instance.SecurityGroupIDs,
	}
	
	// 构建元数据
	metadata := map[string]interface{}{
		"created_at": instance.CreatedAt,
		"provider":   c.account.Provider,
		"region":     instance.Region,
		"zone":       instance.Zone,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeECS,
		Name:           instance.Name,
		CloudID:        instance.ID,
		Region:         instance.Region,
		Zone:           instance.Zone,
		Status:         model.ResourceStatus(instance.Status),
		Tags:           convertTags(instance.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// ConvertVPCToResource 转换VPC为资源
func (c *ResourceConverter) ConvertVPCToResource(vpc *VPC) (*model.Resource, error) {
	now := time.Now()
	
	properties := map[string]interface{}{
		"cidr_block":  vpc.CidrBlock,
		"description": vpc.Description,
	}
	
	metadata := map[string]interface{}{
		"created_at": vpc.CreatedAt,
		"provider":   c.account.Provider,
		"region":     vpc.Region,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeVPC,
		Name:           vpc.Name,
		CloudID:        vpc.ID,
		Region:         vpc.Region,
		Status:         model.ResourceStatus(vpc.Status),
		Tags:           convertTags(vpc.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// ConvertSubnetToResource 转换子网为资源
func (c *ResourceConverter) ConvertSubnetToResource(subnet *Subnet) (*model.Resource, error) {
	now := time.Now()
	
	properties := map[string]interface{}{
		"vpc_id":             subnet.VpcID,
		"cidr_block":         subnet.CidrBlock,
		"available_ip_count": subnet.AvailableIPCount,
		"description":        subnet.Description,
	}
	
	metadata := map[string]interface{}{
		"created_at": subnet.CreatedAt,
		"provider":   c.account.Provider,
		"region":     subnet.Region,
		"zone":       subnet.Zone,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeSubnet,
		Name:           subnet.Name,
		CloudID:        subnet.ID,
		Region:         subnet.Region,
		Zone:           subnet.Zone,
		Status:         model.ResourceStatus(subnet.Status),
		Tags:           convertTags(subnet.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// ConvertLoadBalancerToResource 转换负载均衡器为资源
func (c *ResourceConverter) ConvertLoadBalancerToResource(lb *LoadBalancer) (*model.Resource, error) {
	now := time.Now()
	
	properties := map[string]interface{}{
		"type":       lb.Type,
		"vpc_id":     lb.VpcID,
		"subnet_ids": lb.SubnetIDs,
		"public_ip":  lb.PublicIP,
		"private_ip": lb.PrivateIP,
		"listeners":  lb.Listeners,
	}
	
	metadata := map[string]interface{}{
		"created_at": lb.CreatedAt,
		"provider":   c.account.Provider,
		"region":     lb.Region,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeSLB,
		Name:           lb.Name,
		CloudID:        lb.ID,
		Region:         lb.Region,
		Status:         model.ResourceStatus(lb.Status),
		Tags:           convertTags(lb.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// ConvertDiskToResource 转换云盘为资源
func (c *ResourceConverter) ConvertDiskToResource(disk *Disk) (*model.Resource, error) {
	now := time.Now()
	
	properties := map[string]interface{}{
		"type":        disk.Type,
		"size":        disk.Size,
		"instance_id": disk.InstanceID,
		"device":      disk.Device,
		"encrypted":   disk.Encrypted,
	}
	
	metadata := map[string]interface{}{
		"created_at": disk.CreatedAt,
		"provider":   c.account.Provider,
		"region":     disk.Region,
		"zone":       disk.Zone,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeEBS,
		Name:           disk.Name,
		CloudID:        disk.ID,
		Region:         disk.Region,
		Zone:           disk.Zone,
		Status:         model.ResourceStatus(disk.Status),
		Tags:           convertTags(disk.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// ConvertDatabaseToResource 转换数据库为资源
func (c *ResourceConverter) ConvertDatabaseToResource(db *Database) (*model.Resource, error) {
	now := time.Now()
	
	properties := map[string]interface{}{
		"engine":        db.Engine,
		"version":       db.Version,
		"instance_type": db.InstanceType,
		"cpu":           db.CPU,
		"memory":        db.Memory,
		"storage":       db.Storage,
		"vpc_id":        db.VpcID,
		"subnet_id":     db.SubnetID,
		"public_ip":     db.PublicIP,
		"private_ip":    db.PrivateIP,
		"port":          db.Port,
	}
	
	metadata := map[string]interface{}{
		"created_at": db.CreatedAt,
		"provider":   c.account.Provider,
		"region":     db.Region,
		"zone":       db.Zone,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeRDS,
		Name:           db.Name,
		CloudID:        db.ID,
		Region:         db.Region,
		Zone:           db.Zone,
		Status:         model.ResourceStatus(db.Status),
		Tags:           convertTags(db.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// ConvertSecurityGroupToResource 转换安全组为资源
func (c *ResourceConverter) ConvertSecurityGroupToResource(sg *SecurityGroup) (*model.Resource, error) {
	now := time.Now()
	
	properties := map[string]interface{}{
		"description": sg.Description,
		"vpc_id":      sg.VpcID,
		"rules":       sg.Rules,
	}
	
	metadata := map[string]interface{}{
		"created_at": sg.CreatedAt,
		"provider":   c.account.Provider,
		"region":     sg.Region,
	}
	
	resource := &model.Resource{
		TenantModel: model.TenantModel{
			BaseModel: model.BaseModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			TenantID: c.account.TenantID,
		},
		CloudAccountID: c.account.ID,
		Provider:       c.account.Provider,
		Type:           model.ResourceTypeSG,
		Name:           sg.Name,
		CloudID:        sg.ID,
		Region:         sg.Region,
		Status:         model.ResourceStatusRunning, // 安全组通常是运行状态
		Tags:           convertTags(sg.Tags),
		Properties:     properties,
		Metadata:       metadata,
		LastSyncAt:     &now,
	}
	
	return resource, nil
}

// convertTags 转换标签格式
func convertTags(tags map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range tags {
		result[k] = v
	}
	return result
}
