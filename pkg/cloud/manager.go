package cloud

import (
	"context"
	"fmt"
	"sync"
	"time"

	"cmdb-platform/internal/model"
)

// Manager 云服务管理器
type Manager struct {
	providers map[model.CloudProvider]CloudProvider
	mu        sync.RWMutex
}

// NewManager 创建云服务管理器
func NewManager() *Manager {
	return &Manager{
		providers: make(map[model.CloudProvider]CloudProvider),
	}
}

// RegisterProvider 注册云服务商
func (m *Manager) RegisterProvider(providerType model.CloudProvider, provider CloudProvider) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.providers[providerType] = provider
}

// GetProvider 获取云服务商
func (m *Manager) GetProvider(providerType model.CloudProvider) (CloudProvider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	provider, exists := m.providers[providerType]
	if !exists {
		return nil, fmt.Errorf("provider %s not found", providerType)
	}

	return provider, nil
}

// ListProviders 列出所有已注册的云服务商
func (m *Manager) ListProviders() []model.CloudProvider {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var providers []model.CloudProvider
	for providerType := range m.providers {
		providers = append(providers, providerType)
	}

	return providers
}

// SyncResources 同步指定云账号的资源
func (m *Manager) SyncResources(ctx context.Context, account *model.CloudAccount, resourceTypes []model.ResourceType) (*SyncResult, error) {
	provider, err := m.GetProvider(account.Provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get provider: %w", err)
	}

	result := &SyncResult{
		CloudAccountID: account.ID,
		Provider:       account.Provider,
		StartTime:      time.Now(),
		Resources:      make([]model.Resource, 0),
		Errors:         make([]error, 0),
	}

	// 如果没有指定资源类型，则同步所有支持的类型
	if len(resourceTypes) == 0 {
		resourceTypes = []model.ResourceType{
			model.ResourceTypeECS,
			model.ResourceTypeVPC,
			model.ResourceTypeSubnet,
			model.ResourceTypeSLB,
			model.ResourceTypeEBS,
			model.ResourceTypeRDS,
			model.ResourceTypeSG,
		}
	}

	for _, resourceType := range resourceTypes {
		switch resourceType {
		case model.ResourceTypeECS:
			if err := m.syncInstances(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync instances failed: %w", err))
			}
		case model.ResourceTypeVPC:
			if err := m.syncVPCs(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync vpcs failed: %w", err))
			}
		case model.ResourceTypeSubnet:
			if err := m.syncSubnets(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync subnets failed: %w", err))
			}
		case model.ResourceTypeSLB:
			if err := m.syncLoadBalancers(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync load balancers failed: %w", err))
			}
		case model.ResourceTypeEBS:
			if err := m.syncDisks(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync disks failed: %w", err))
			}
		case model.ResourceTypeRDS:
			if err := m.syncDatabases(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync databases failed: %w", err))
			}
		case model.ResourceTypeSG:
			if err := m.syncSecurityGroups(ctx, provider, account, result); err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("sync security groups failed: %w", err))
			}
		}
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.TotalCount = len(result.Resources)
	result.SuccessCount = result.TotalCount - len(result.Errors)
	result.FailureCount = len(result.Errors)

	return result, nil
}

// syncInstances 同步云服务器实例
func (m *Manager) syncInstances(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	instances, err := provider.ListInstances(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, instance := range instances {
		resource, err := converter.ConvertInstanceToResource(&instance)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("convert instance %s failed: %w", instance.ID, err))
			continue
		}
		result.Resources = append(result.Resources, *resource)
	}

	return nil
}

// syncVPCs 同步VPC
func (m *Manager) syncVPCs(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	vpcs, err := provider.ListVPCs(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, vpc := range vpcs {
		resource, err := converter.ConvertVPCToResource(&vpc)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("convert vpc %s failed: %w", vpc.ID, err))
			continue
		}
		result.Resources = append(result.Resources, *resource)
	}

	return nil
}

// syncSubnets 同步子网
func (m *Manager) syncSubnets(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	// 首先获取所有VPC
	vpcs, err := provider.ListVPCs(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, vpc := range vpcs {
		subnets, err := provider.ListSubnets(ctx, account.Region, vpc.ID)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("list subnets for vpc %s failed: %w", vpc.ID, err))
			continue
		}

		for _, subnet := range subnets {
			resource, err := converter.ConvertSubnetToResource(&subnet)
			if err != nil {
				result.Errors = append(result.Errors, fmt.Errorf("convert subnet %s failed: %w", subnet.ID, err))
				continue
			}
			result.Resources = append(result.Resources, *resource)
		}
	}

	return nil
}

// syncLoadBalancers 同步负载均衡器
func (m *Manager) syncLoadBalancers(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	lbs, err := provider.ListLoadBalancers(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, lb := range lbs {
		resource, err := converter.ConvertLoadBalancerToResource(&lb)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("convert load balancer %s failed: %w", lb.ID, err))
			continue
		}
		result.Resources = append(result.Resources, *resource)
	}

	return nil
}

// syncDisks 同步云盘
func (m *Manager) syncDisks(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	disks, err := provider.ListDisks(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, disk := range disks {
		resource, err := converter.ConvertDiskToResource(&disk)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("convert disk %s failed: %w", disk.ID, err))
			continue
		}
		result.Resources = append(result.Resources, *resource)
	}

	return nil
}

// syncDatabases 同步数据库
func (m *Manager) syncDatabases(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	databases, err := provider.ListDatabases(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, database := range databases {
		resource, err := converter.ConvertDatabaseToResource(&database)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("convert database %s failed: %w", database.ID, err))
			continue
		}
		result.Resources = append(result.Resources, *resource)
	}

	return nil
}

// syncSecurityGroups 同步安全组
func (m *Manager) syncSecurityGroups(ctx context.Context, provider CloudProvider, account *model.CloudAccount, result *SyncResult) error {
	sgs, err := provider.ListSecurityGroups(ctx, account.Region)
	if err != nil {
		return err
	}

	converter := NewResourceConverter(account)
	for _, sg := range sgs {
		resource, err := converter.ConvertSecurityGroupToResource(&sg)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Errorf("convert security group %s failed: %w", sg.ID, err))
			continue
		}
		result.Resources = append(result.Resources, *resource)
	}

	return nil
}
