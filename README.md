# 多云CMDB管理平台

一个基于Go Gin框架的企业级多云配置管理数据库(CMDB)平台，支持阿里云、腾讯云、AWS等主流云服务商。

## 功能特性

### 🌐 多云支持
- **阿里云**：ECS、RDS、SLB、VPC等资源管理
- **腾讯云**：CVM、CDB、CLB、VPC等资源管理  
- **AWS**：EC2、RDS、ELB、VPC等资源管理
- 统一的资源模型和API接口

### 📊 核心功能
- **资源发现**：自动发现和同步云资源
- **配置管理**：统一的配置项管理
- **关系映射**：资源间依赖关系可视化
- **变更追踪**：配置变更历史记录
- **告警通知**：资源状态监控和告警

### 🔐 安全特性
- **多租户**：支持多组织隔离
- **RBAC权限**：基于角色的访问控制
- **API安全**：JWT认证 + HTTPS
- **审计日志**：完整的操作审计

### 🎯 技术架构
- **后端**：Go + Gin + GORM
- **数据库**：PostgreSQL + Redis
- **前端**：React/Vue.js (待开发)
- **部署**：Docker + Kubernetes

## 项目结构

```
cmdb-platform/
├── cmd/                    # 应用入口
│   └── server/            # 服务器启动
├── internal/              # 内部包
│   ├── api/              # API路由和处理器
│   ├── service/          # 业务逻辑层
│   ├── repository/       # 数据访问层
│   ├── model/            # 数据模型
│   ├── middleware/       # 中间件
│   └── config/           # 配置管理
├── pkg/                   # 公共包
│   ├── cloud/            # 云服务适配器
│   ├── auth/             # 认证授权
│   ├── logger/           # 日志组件
│   └── utils/            # 工具函数
├── web/                   # 前端资源
├── configs/               # 配置文件
├── scripts/               # 部署脚本
├── docs/                  # 文档
└── tests/                 # 测试文件
```

## 快速开始

### 环境要求
- Go 1.21+
- PostgreSQL 13+
- Redis 6+
- Docker (可选)

### 安装依赖
```bash
go mod tidy
```

### 配置数据库
```bash
# 创建数据库
createdb cmdb_platform

# 配置环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=your_password
export DB_NAME=cmdb_platform
```

### 运行服务
```bash
go run cmd/server/main.go
```

### API文档
启动服务后访问：http://localhost:8080/swagger/index.html

## 云服务配置

### 阿里云
```yaml
aliyun:
  access_key_id: "your_access_key"
  access_key_secret: "your_secret"
  region: "cn-hangzhou"
```

### 腾讯云
```yaml
tencent:
  secret_id: "your_secret_id"
  secret_key: "your_secret_key"
  region: "ap-guangzhou"
```

### AWS
```yaml
aws:
  access_key_id: "your_access_key"
  secret_access_key: "your_secret"
  region: "us-west-2"
```

## 开发指南

### 添加新的云服务商
1. 在 `pkg/cloud/` 下创建新的适配器
2. 实现 `CloudProvider` 接口
3. 注册到云服务管理器

### 扩展资源类型
1. 在 `internal/model/` 下定义新的资源模型
2. 实现对应的服务和仓库层
3. 添加API路由

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
