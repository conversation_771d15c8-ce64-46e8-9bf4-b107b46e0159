# CMDB平台配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"  # debug, release, test
  read_timeout: 60s
  write_timeout: 60s

# 数据库配置
database:
  postgres:
    host: "localhost"
    port: 5432
    user: "postgres"
    password: "password"
    dbname: "cmdb_platform"
    sslmode: "disable"
    timezone: "Asia/Shanghai"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
  
  redis:
    host: "localhost"
    port: 6379
    password: ""
    db: 0
    pool_size: 10
    min_idle_conns: 5

# JWT配置
jwt:
  secret: "your-jwt-secret-key"
  expire_hours: 24
  refresh_expire_hours: 168  # 7天

# 日志配置
log:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "stdout"  # stdout, file
  file_path: "logs/cmdb.log"
  max_size: 100  # MB
  max_backups: 5
  max_age: 30  # days
  compress: true

# 云服务商配置
cloud_providers:
  aliyun:
    enabled: true
    access_key_id: "${ALIYUN_ACCESS_KEY_ID}"
    access_key_secret: "${ALIYUN_ACCESS_KEY_SECRET}"
    region: "cn-hangzhou"
    sync_interval: "5m"
    
  tencent:
    enabled: true
    secret_id: "${TENCENT_SECRET_ID}"
    secret_key: "${TENCENT_SECRET_KEY}"
    region: "ap-guangzhou"
    sync_interval: "5m"
    
  aws:
    enabled: true
    access_key_id: "${AWS_ACCESS_KEY_ID}"
    secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
    region: "us-west-2"
    sync_interval: "5m"

# 同步配置
sync:
  enabled: true
  interval: "5m"
  batch_size: 100
  timeout: "30s"
  retry_times: 3
  retry_interval: "10s"

# 告警配置
alert:
  enabled: true
  webhook_url: ""
  email:
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""
    from: "<EMAIL>"

# 监控配置
monitoring:
  enabled: true
  metrics_path: "/metrics"
  health_path: "/health"

# CORS配置
cors:
  allow_origins: ["*"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["*"]
  expose_headers: ["*"]
  allow_credentials: true
  max_age: 12h

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 100
  burst: 200

# 缓存配置
cache:
  default_expiration: "1h"
  cleanup_interval: "10m"
  
# 分页配置
pagination:
  default_page_size: 20
  max_page_size: 100
