# CMDB Platform Makefile

# 变量定义
APP_NAME := cmdb-platform
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "v1.0.0")
BUILD_TIME := $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS := -X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)

# Go相关变量
GO := go
GOFLAGS := -ldflags "$(LDFLAGS)"
BINARY_DIR := bin
BINARY := $(BINARY_DIR)/$(APP_NAME)

# Docker相关变量
DOCKER_IMAGE := $(APP_NAME):$(VERSION)
DOCKER_REGISTRY := your-registry.com

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	$(GO) mod download
	$(GO) mod tidy

# 代码格式化
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GO) fmt ./...

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	golangci-lint run

# 运行测试
.PHONY: test
test:
	@echo "Running tests..."
	$(GO) test -v -race -coverprofile=coverage.out ./...

# 查看测试覆盖率
.PHONY: coverage
coverage: test
	@echo "Generating coverage report..."
	$(GO) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 构建二进制文件
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BINARY_DIR)
	$(GO) build $(GOFLAGS) -o $(BINARY) cmd/server/main.go

# 构建Linux版本
.PHONY: build-linux
build-linux:
	@echo "Building $(APP_NAME) for Linux..."
	@mkdir -p $(BINARY_DIR)
	GOOS=linux GOARCH=amd64 $(GO) build $(GOFLAGS) -o $(BINARY)-linux cmd/server/main.go

# 运行应用
.PHONY: run
run: build
	@echo "Running $(APP_NAME)..."
	./$(BINARY) -config=configs/config.yaml

# 开发模式运行
.PHONY: dev
dev:
	@echo "Running in development mode..."
	$(GO) run cmd/server/main.go -config=configs/config.yaml

# 生成Swagger文档
.PHONY: swagger
swagger:
	@echo "Generating Swagger documentation..."
	swag init -g cmd/server/main.go -o docs

# 构建Docker镜像
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

# 推送Docker镜像
.PHONY: docker-push
docker-push: docker-build
	@echo "Pushing Docker image..."
	docker tag $(DOCKER_IMAGE) $(DOCKER_REGISTRY)/$(DOCKER_IMAGE)
	docker push $(DOCKER_REGISTRY)/$(DOCKER_IMAGE)

# 运行Docker容器
.PHONY: docker-run
docker-run: docker-build
	@echo "Running Docker container..."
	docker run -p 8080:8080 -v $(PWD)/configs:/app/configs $(DOCKER_IMAGE)

# 启动开发环境
.PHONY: dev-env
dev-env:
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d

# 停止开发环境
.PHONY: dev-env-down
dev-env-down:
	@echo "Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down

# 数据库迁移
.PHONY: migrate
migrate:
	@echo "Running database migration..."
	$(GO) run cmd/migrate/main.go -config=configs/config.yaml

# 生成模拟数据
.PHONY: seed
seed:
	@echo "Seeding database..."
	$(GO) run cmd/seed/main.go -config=configs/config.yaml

# 清理构建文件
.PHONY: clean
clean:
	@echo "Cleaning up..."
	rm -rf $(BINARY_DIR)
	rm -f coverage.out coverage.html
	$(GO) clean

# 安装开发工具
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GO) install github.com/swaggo/swag/cmd/swag@latest
	$(GO) install github.com/golang/mock/mockgen@latest

# 生成Mock文件
.PHONY: mock
mock:
	@echo "Generating mock files..."
	mockgen -source=internal/repository/resource.go -destination=internal/repository/mocks/resource_mock.go
	mockgen -source=internal/service/resource.go -destination=internal/service/mocks/resource_mock.go

# 检查代码质量
.PHONY: check
check: fmt lint test

# 完整构建流程
.PHONY: ci
ci: clean deps check build

# 显示帮助信息
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, install deps and build"
	@echo "  deps         - Install dependencies"
	@echo "  fmt          - Format code"
	@echo "  lint         - Run linter"
	@echo "  test         - Run tests"
	@echo "  coverage     - Generate test coverage report"
	@echo "  build        - Build binary"
	@echo "  build-linux  - Build Linux binary"
	@echo "  run          - Build and run application"
	@echo "  dev          - Run in development mode"
	@echo "  swagger      - Generate Swagger documentation"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-push  - Push Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  dev-env      - Start development environment"
	@echo "  dev-env-down - Stop development environment"
	@echo "  migrate      - Run database migration"
	@echo "  seed         - Seed database with test data"
	@echo "  clean        - Clean build files"
	@echo "  install-tools- Install development tools"
	@echo "  mock         - Generate mock files"
	@echo "  check        - Run all checks (fmt, lint, test)"
	@echo "  ci           - Full CI pipeline"
	@echo "  help         - Show this help message"
