package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// TokenBucket 令牌桶
type TokenBucket struct {
	capacity int
	tokens   int
	rate     int
	lastTime time.Time
	mutex    sync.Mutex
}

// NewTokenBucket 创建令牌桶
func NewTokenBucket(capacity, rate int) *TokenBucket {
	return &TokenBucket{
		capacity: capacity,
		tokens:   capacity,
		rate:     rate,
		lastTime: time.Now(),
	}
}

// Allow 检查是否允许请求
func (tb *TokenBucket) Allow() bool {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()
	
	now := time.Now()
	elapsed := now.Sub(tb.lastTime)
	
	// 添加令牌
	tokensToAdd := int(elapsed.Seconds()) * tb.rate / 60 // 每分钟的速率
	tb.tokens += tokensToAdd
	if tb.tokens > tb.capacity {
		tb.tokens = tb.capacity
	}
	
	tb.lastTime = now
	
	// 检查是否有令牌
	if tb.tokens > 0 {
		tb.tokens--
		return true
	}
	
	return false
}

// RateLimit 限流中间件
func RateLimit(requestsPerMinute, burst int) gin.HandlerFunc {
	buckets := make(map[string]*TokenBucket)
	var mutex sync.RWMutex
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		mutex.RLock()
		bucket, exists := buckets[clientIP]
		mutex.RUnlock()
		
		if !exists {
			mutex.Lock()
			bucket = NewTokenBucket(burst, requestsPerMinute)
			buckets[clientIP] = bucket
			mutex.Unlock()
		}
		
		if !bucket.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    http.StatusTooManyRequests,
				"message": "Too many requests",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}
