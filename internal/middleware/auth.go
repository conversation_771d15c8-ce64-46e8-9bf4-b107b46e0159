package middleware

import (
	"errors"
	"net/http"
	"strings"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/auth"
	"cmdb-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	jwtManager *auth.JWTManager
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(jwtManager *auth.JWTManager) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: jwtManager,
	}
}

// RequireAuth 需要认证的中间件
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := m.extractToken(c)
		if token == "" {
			c.JSON(http.StatusUnauthorized, model.ErrorResponse{
				Code:    http.StatusUnauthorized,
				Message: "Missing authorization token",
			})
			c.Abort()
			return
		}

		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			logger.Warnf("Invalid token: %v", err)
			c.J<PERSON>(http.StatusUnauthorized, model.ErrorResponse{
				Code:    http.StatusUnauthorized,
				Message: "Invalid authorization token",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)
		c.Set("tenant_id", claims.TenantID)

		c.Next()
	}
}

// RequireRole 需要特定角色的中间件
func (m *AuthMiddleware) RequireRole(roles ...model.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, model.ErrorResponse{
				Code:    http.StatusUnauthorized,
				Message: "User role not found",
			})
			c.Abort()
			return
		}

		role, ok := userRole.(model.UserRole)
		if !ok {
			c.JSON(http.StatusInternalServerError, model.ErrorResponse{
				Code:    http.StatusInternalServerError,
				Message: "Invalid user role type",
			})
			c.Abort()
			return
		}

		// 检查用户角色是否在允许的角色列表中
		for _, allowedRole := range roles {
			if role == allowedRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, model.ErrorResponse{
			Code:    http.StatusForbidden,
			Message: "Insufficient permissions",
		})
		c.Abort()
	}
}

// RequireAdmin 需要管理员权限的中间件
func (m *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return m.RequireRole(model.UserRoleAdmin)
}

// RequireOperator 需要操作员权限的中间件
func (m *AuthMiddleware) RequireOperator() gin.HandlerFunc {
	return m.RequireRole(model.UserRoleAdmin, model.UserRoleOperator)
}

// OptionalAuth 可选认证的中间件
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := m.extractToken(c)
		if token != "" {
			claims, err := m.jwtManager.ValidateToken(token)
			if err == nil {
				// 将用户信息存储到上下文中
				c.Set("user_id", claims.UserID)
				c.Set("username", claims.Username)
				c.Set("email", claims.Email)
				c.Set("role", claims.Role)
				c.Set("tenant_id", claims.TenantID)
			}
		}

		c.Next()
	}
}

// extractToken 从请求中提取令牌
func (m *AuthMiddleware) extractToken(c *gin.Context) string {
	// 从Authorization头中提取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// Bearer token格式
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
		// 直接返回token
		return authHeader
	}

	// 从查询参数中提取
	token := c.Query("token")
	if token != "" {
		return token
	}

	// 从Cookie中提取
	cookie, err := c.Cookie("access_token")
	if err == nil && cookie != "" {
		return cookie
	}

	return ""
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) (*CurrentUser, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return nil, ErrUserNotFound
	}

	id, ok := userID.(uuid.UUID)
	if !ok {
		return nil, ErrInvalidUserID
	}

	username, _ := c.Get("username")
	email, _ := c.Get("email")
	role, _ := c.Get("role")
	tenantID, _ := c.Get("tenant_id")

	return &CurrentUser{
		ID:       id,
		Username: username.(string),
		Email:    email.(string),
		Role:     role.(model.UserRole),
		TenantID: tenantID.(uuid.UUID),
	}, nil
}

// GetCurrentUserID 获取当前用户ID
func GetCurrentUserID(c *gin.Context) (uuid.UUID, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, ErrUserNotFound
	}

	id, ok := userID.(uuid.UUID)
	if !ok {
		return uuid.Nil, ErrInvalidUserID
	}

	return id, nil
}

// GetCurrentTenantID 获取当前租户ID
func GetCurrentTenantID(c *gin.Context) (uuid.UUID, error) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		return uuid.Nil, ErrTenantNotFound
	}

	id, ok := tenantID.(uuid.UUID)
	if !ok {
		return uuid.Nil, ErrInvalidTenantID
	}

	return id, nil
}

// CurrentUser 当前用户信息
type CurrentUser struct {
	ID       uuid.UUID      `json:"id"`
	Username string         `json:"username"`
	Email    string         `json:"email"`
	Role     model.UserRole `json:"role"`
	TenantID uuid.UUID      `json:"tenant_id"`
}

// 错误定义
var (
	ErrUserNotFound    = errors.New("user not found in context")
	ErrInvalidUserID   = errors.New("invalid user ID type")
	ErrTenantNotFound  = errors.New("tenant not found in context")
	ErrInvalidTenantID = errors.New("invalid tenant ID type")
)
