package repository

import (
	"context"

	"cmdb-platform/internal/model"
	"cmdb-platform/pkg/logger"
	
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SyncTaskRepository 同步任务仓库接口
type SyncTaskRepository interface {
	Create(ctx context.Context, task *model.SyncTask) error
	Update(ctx context.Context, task *model.SyncTask) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.SyncTask, error)
	List(ctx context.Context, tenantID uuid.UUID, limit, offset int) ([]model.SyncTask, int64, error)
	ListByCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) ([]model.SyncTask, error)
	ListByStatus(ctx context.Context, status model.SyncStatus) ([]model.SyncTask, error)
}

// syncTaskRepository 同步任务仓库实现
type syncTaskRepository struct {
	db *gorm.DB
}

// NewSyncTaskRepository 创建同步任务仓库
func NewSyncTaskRepository(db *gorm.DB) SyncTaskRepository {
	return &syncTaskRepository{db: db}
}

// Create 创建同步任务
func (r *syncTaskRepository) Create(ctx context.Context, task *model.SyncTask) error {
	if err := r.db.WithContext(ctx).Create(task).Error; err != nil {
		logger.Errorf("Failed to create sync task: %v", err)
		return err
	}
	return nil
}

// Update 更新同步任务
func (r *syncTaskRepository) Update(ctx context.Context, task *model.SyncTask) error {
	if err := r.db.WithContext(ctx).Save(task).Error; err != nil {
		logger.Errorf("Failed to update sync task: %v", err)
		return err
	}
	return nil
}

// Delete 删除同步任务
func (r *syncTaskRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&model.SyncTask{}, id).Error; err != nil {
		logger.Errorf("Failed to delete sync task: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取同步任务
func (r *syncTaskRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.SyncTask, error) {
	var task model.SyncTask
	if err := r.db.WithContext(ctx).
		Preload("CloudAccount").
		First(&task, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Errorf("Failed to get sync task by ID: %v", err)
		return nil, err
	}
	return &task, nil
}

// List 列出同步任务
func (r *syncTaskRepository) List(ctx context.Context, tenantID uuid.UUID, limit, offset int) ([]model.SyncTask, int64, error) {
	var tasks []model.SyncTask
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.SyncTask{}).Where("tenant_id = ?", tenantID)
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("Failed to count sync tasks: %v", err)
		return nil, 0, err
	}
	
	// 获取数据
	if err := db.Preload("CloudAccount").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tasks).Error; err != nil {
		logger.Errorf("Failed to list sync tasks: %v", err)
		return nil, 0, err
	}
	
	return tasks, total, nil
}

// ListByCloudAccount 根据云账号列出同步任务
func (r *syncTaskRepository) ListByCloudAccount(ctx context.Context, cloudAccountID uuid.UUID) ([]model.SyncTask, error) {
	var tasks []model.SyncTask
	if err := r.db.WithContext(ctx).
		Where("cloud_account_id = ?", cloudAccountID).
		Order("created_at DESC").
		Find(&tasks).Error; err != nil {
		logger.Errorf("Failed to list sync tasks by cloud account: %v", err)
		return nil, err
	}
	return tasks, nil
}

// ListByStatus 根据状态列出同步任务
func (r *syncTaskRepository) ListByStatus(ctx context.Context, status model.SyncStatus) ([]model.SyncTask, error) {
	var tasks []model.SyncTask
	if err := r.db.WithContext(ctx).
		Where("status = ?", status).
		Order("created_at DESC").
		Find(&tasks).Error; err != nil {
		logger.Errorf("Failed to list sync tasks by status: %v", err)
		return nil, err
	}
	return tasks, nil
}
