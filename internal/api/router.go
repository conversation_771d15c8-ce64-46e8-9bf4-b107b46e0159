package api

import (
	"fmt"

	"cmdb-platform/internal/config"
	"cmdb-platform/internal/middleware"
	"cmdb-platform/internal/service"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Router 路由管理器
type Router struct {
	engine          *gin.Engine
	config          *config.Config
	resourceHandler *ResourceHandler
	healthHandler   *HealthHandler
}

// NewRouter 创建路由管理器
func NewRouter(
	cfg *config.Config,
	resourceService service.ResourceService,
) *Router {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())

	// CORS配置
	corsConfig := cors.Config{
		AllowOrigins:     cfg.CORS.AllowOrigins,
		AllowMethods:     cfg.CORS.AllowMethods,
		AllowHeaders:     cfg.CORS.AllowHeaders,
		ExposeHeaders:    cfg.CORS.ExposeHeaders,
		AllowCredentials: cfg.CORS.AllowCredentials,
		MaxAge:           cfg.CORS.MaxAge,
	}
	engine.Use(cors.New(corsConfig))

	// 限流中间件
	if cfg.RateLimit.Enabled {
		engine.Use(middleware.RateLimit(cfg.RateLimit.RequestsPerMinute, cfg.RateLimit.Burst))
	}

	return &Router{
		engine:          engine,
		config:          cfg,
		resourceHandler: NewResourceHandler(resourceService),
		healthHandler:   NewHealthHandler(),
	}
}

// SetupRoutes 设置路由
func (r *Router) SetupRoutes() {
	// 健康检查
	r.engine.GET("/health", r.healthHandler.Health)
	r.engine.GET("/ready", r.healthHandler.Ready)

	// Swagger文档
	r.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API路由组
	v1 := r.engine.Group("/api/v1")
	{
		// 资源管理
		resources := v1.Group("/resources")
		{
			resources.POST("", r.resourceHandler.CreateResource)
			resources.GET("", r.resourceHandler.ListResources)
			resources.GET("/stats", r.resourceHandler.GetResourceStats)
			resources.POST("/sync", r.resourceHandler.SyncResources)
			resources.GET("/:id", r.resourceHandler.GetResource)
			resources.PUT("/:id", r.resourceHandler.UpdateResource)
			resources.DELETE("/:id", r.resourceHandler.DeleteResource)
		}

		// TODO: 添加其他API路由
		// - 云账号管理 /cloud-accounts
		// - 配置项管理 /config-items
		// - 资源关系 /resource-relations
		// - 告警管理 /alerts
		// - 同步任务 /sync-tasks
		// - 用户管理 /users
		// - 租户管理 /tenants
	}
}

// GetEngine 获取Gin引擎
func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}

// Run 启动服务器
func (r *Router) Run() error {
	addr := fmt.Sprintf("%s:%d", r.config.Server.Host, r.config.Server.Port)
	return r.engine.Run(addr)
}
