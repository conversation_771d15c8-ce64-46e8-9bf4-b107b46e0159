package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server         ServerConfig         `mapstructure:"server"`
	Database       DatabaseConfig       `mapstructure:"database"`
	JWT            JWTConfig           `mapstructure:"jwt"`
	Log            LogConfig           `mapstructure:"log"`
	CloudProviders CloudProvidersConfig `mapstructure:"cloud_providers"`
	Sync           SyncConfig          `mapstructure:"sync"`
	Alert          AlertConfig         `mapstructure:"alert"`
	Monitoring     MonitoringConfig    `mapstructure:"monitoring"`
	CORS           CORSConfig          `mapstructure:"cors"`
	RateLimit      RateLimitConfig     `mapstructure:"rate_limit"`
	Cache          CacheConfig         `mapstructure:"cache"`
	Pagination     PaginationConfig    `mapstructure:"pagination"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Mode         string        `mapstructure:"mode"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Postgres PostgresConfig `mapstructure:"postgres"`
	Redis    RedisConfig    `mapstructure:"redis"`
}

// PostgresConfig PostgreSQL配置
type PostgresConfig struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	User            string        `mapstructure:"user"`
	Password        string        `mapstructure:"password"`
	DBName          string        `mapstructure:"dbname"`
	SSLMode         string        `mapstructure:"sslmode"`
	TimeZone        string        `mapstructure:"timezone"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Password     string `mapstructure:"password"`
	DB           int    `mapstructure:"db"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret             string `mapstructure:"secret"`
	ExpireHours        int    `mapstructure:"expire_hours"`
	RefreshExpireHours int    `mapstructure:"refresh_expire_hours"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// CloudProvidersConfig 云服务商配置
type CloudProvidersConfig struct {
	Aliyun  CloudProviderConfig `mapstructure:"aliyun"`
	Tencent CloudProviderConfig `mapstructure:"tencent"`
	AWS     CloudProviderConfig `mapstructure:"aws"`
}

// CloudProviderConfig 单个云服务商配置
type CloudProviderConfig struct {
	Enabled           bool          `mapstructure:"enabled"`
	AccessKeyID       string        `mapstructure:"access_key_id"`
	AccessKeySecret   string        `mapstructure:"access_key_secret"`
	SecretID          string        `mapstructure:"secret_id"`
	SecretKey         string        `mapstructure:"secret_key"`
	SecretAccessKey   string        `mapstructure:"secret_access_key"`
	Region            string        `mapstructure:"region"`
	SyncInterval      time.Duration `mapstructure:"sync_interval"`
}

// SyncConfig 同步配置
type SyncConfig struct {
	Enabled       bool          `mapstructure:"enabled"`
	Interval      time.Duration `mapstructure:"interval"`
	BatchSize     int           `mapstructure:"batch_size"`
	Timeout       time.Duration `mapstructure:"timeout"`
	RetryTimes    int           `mapstructure:"retry_times"`
	RetryInterval time.Duration `mapstructure:"retry_interval"`
}

// AlertConfig 告警配置
type AlertConfig struct {
	Enabled    bool        `mapstructure:"enabled"`
	WebhookURL string      `mapstructure:"webhook_url"`
	Email      EmailConfig `mapstructure:"email"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	SMTPHost string `mapstructure:"smtp_host"`
	SMTPPort int    `mapstructure:"smtp_port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	From     string `mapstructure:"from"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	MetricsPath string `mapstructure:"metrics_path"`
	HealthPath  string `mapstructure:"health_path"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins     []string      `mapstructure:"allow_origins"`
	AllowMethods     []string      `mapstructure:"allow_methods"`
	AllowHeaders     []string      `mapstructure:"allow_headers"`
	ExposeHeaders    []string      `mapstructure:"expose_headers"`
	AllowCredentials bool          `mapstructure:"allow_credentials"`
	MaxAge           time.Duration `mapstructure:"max_age"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled            bool `mapstructure:"enabled"`
	RequestsPerMinute  int  `mapstructure:"requests_per_minute"`
	Burst              int  `mapstructure:"burst"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	DefaultExpiration time.Duration `mapstructure:"default_expiration"`
	CleanupInterval   time.Duration `mapstructure:"cleanup_interval"`
}

// PaginationConfig 分页配置
type PaginationConfig struct {
	DefaultPageSize int `mapstructure:"default_page_size"`
	MaxPageSize     int `mapstructure:"max_page_size"`
}

// Load 加载配置
func Load(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// GetDSN 获取PostgreSQL连接字符串
func (c *PostgresConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		c.Host, c.Port, c.User, c.Password, c.DBName, c.SSLMode, c.TimeZone)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}
