package model

import (
	"time"

	"github.com/google/uuid"
)

// User 用户模型
type User struct {
	BaseModel
	Username    string     `json:"username" gorm:"uniqueIndex;not null;size:50"`
	Email       string     `json:"email" gorm:"uniqueIndex;not null;size:100"`
	Password    string     `json:"-" gorm:"not null;size:255"`
	DisplayName string     `json:"display_name" gorm:"size:100"`
	Avatar      string     `json:"avatar" gorm:"size:255"`
	Phone       string     `json:"phone" gorm:"size:20"`
	Role        UserRole   `json:"role" gorm:"not null;default:'viewer'"`
	Status      UserStatus `json:"status" gorm:"not null;default:'active'"`
	LastLoginAt *time.Time `json:"last_login_at"`
	
	// 关联关系
	Tenants []UserTenant `json:"tenants,omitempty" gorm:"foreignKey:UserID"`
}

// Tenant 租户模型
type Tenant struct {
	BaseModel
	Name        string `json:"name" gorm:"not null;size:100"`
	Code        string `json:"code" gorm:"uniqueIndex;not null;size:50"`
	Description string `json:"description" gorm:"size:500"`
	Status      string `json:"status" gorm:"not null;default:'active'"`
	
	// 关联关系
	Users []UserTenant `json:"users,omitempty" gorm:"foreignKey:TenantID"`
}

// UserTenant 用户租户关联模型
type UserTenant struct {
	BaseModel
	UserID   uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	TenantID uuid.UUID `json:"tenant_id" gorm:"type:uuid;not null"`
	Role     UserRole  `json:"role" gorm:"not null;default:'viewer'"`
	
	// 关联关系
	User   User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Tenant Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// CloudAccount 云账号模型
type CloudAccount struct {
	TenantModel
	Name            string        `json:"name" gorm:"not null;size:100"`
	Provider        CloudProvider `json:"provider" gorm:"not null"`
	AccessKeyID     string        `json:"access_key_id" gorm:"not null;size:255"`
	AccessKeySecret string        `json:"-" gorm:"not null;size:255"`
	Region          string        `json:"region" gorm:"not null;size:50"`
	Description     string        `json:"description" gorm:"size:500"`
	Status          string        `json:"status" gorm:"not null;default:'active'"`
	LastSyncAt      *time.Time    `json:"last_sync_at"`
	
	// 关联关系
	Resources []Resource `json:"resources,omitempty" gorm:"foreignKey:CloudAccountID"`
}

// Permission 权限模型
type Permission struct {
	BaseModel
	Name        string `json:"name" gorm:"uniqueIndex;not null;size:100"`
	Code        string `json:"code" gorm:"uniqueIndex;not null;size:100"`
	Description string `json:"description" gorm:"size:500"`
	Resource    string `json:"resource" gorm:"not null;size:100"`
	Action      string `json:"action" gorm:"not null;size:100"`
}

// Role 角色模型
type Role struct {
	BaseModel
	Name        string `json:"name" gorm:"uniqueIndex;not null;size:100"`
	Code        string `json:"code" gorm:"uniqueIndex;not null;size:100"`
	Description string `json:"description" gorm:"size:500"`
	
	// 关联关系
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
}

// UserSession 用户会话模型
type UserSession struct {
	BaseModel
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	Token     string    `json:"token" gorm:"uniqueIndex;not null;size:255"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	IPAddress string    `json:"ip_address" gorm:"size:45"`
	UserAgent string    `json:"user_agent" gorm:"size:500"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// AuditLog 审计日志模型
type AuditLog struct {
	BaseModel
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid"`
	TenantID    uuid.UUID `json:"tenant_id" gorm:"type:uuid"`
	Action      string    `json:"action" gorm:"not null;size:100"`
	Resource    string    `json:"resource" gorm:"not null;size:100"`
	ResourceID  uuid.UUID `json:"resource_id" gorm:"type:uuid"`
	OldValue    string    `json:"old_value" gorm:"type:text"`
	NewValue    string    `json:"new_value" gorm:"type:text"`
	IPAddress   string    `json:"ip_address" gorm:"size:45"`
	UserAgent   string    `json:"user_agent" gorm:"size:500"`
	Description string    `json:"description" gorm:"size:1000"`
	
	// 关联关系
	User   User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Tenant Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

func (Tenant) TableName() string {
	return "tenants"
}

func (UserTenant) TableName() string {
	return "user_tenants"
}

func (CloudAccount) TableName() string {
	return "cloud_accounts"
}

func (Permission) TableName() string {
	return "permissions"
}

func (Role) TableName() string {
	return "roles"
}

func (UserSession) TableName() string {
	return "user_sessions"
}

func (AuditLog) TableName() string {
	return "audit_logs"
}
