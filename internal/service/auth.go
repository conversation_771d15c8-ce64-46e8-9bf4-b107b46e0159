package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"cmdb-platform/internal/model"
	"cmdb-platform/internal/repository"
	"cmdb-platform/pkg/auth"
	"cmdb-platform/pkg/logger"

	"github.com/google/uuid"
)

// AuthService 认证服务接口
type AuthService interface {
	Login(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*model.LoginResponse, error)
	Logout(ctx context.Context, userID uuid.UUID, token string) error
	ChangePassword(ctx context.Context, userID uuid.UUID, req *model.ChangePasswordRequest) error
	CreateUser(ctx context.Context, req *model.CreateUserRequest) (*model.User, error)
	UpdateUser(ctx context.Context, id uuid.UUID, req *model.UpdateUserRequest) (*model.User, error)
	GetUser(ctx context.Context, id uuid.UUID) (*model.User, error)
	ListUsers(ctx context.Context, page, pageSize int) (*model.ListResponse, error)
}

// authService 认证服务实现
type authService struct {
	userRepo        repository.UserRepository
	userTenantRepo  repository.UserTenantRepository
	sessionRepo     repository.UserSessionRepository
	jwtManager      *auth.JWTManager
	passwordManager *auth.PasswordManager
}

// NewAuthService 创建认证服务
func NewAuthService(
	userRepo repository.UserRepository,
	userTenantRepo repository.UserTenantRepository,
	sessionRepo repository.UserSessionRepository,
	jwtManager *auth.JWTManager,
	passwordManager *auth.PasswordManager,
) AuthService {
	return &authService{
		userRepo:        userRepo,
		userTenantRepo:  userTenantRepo,
		sessionRepo:     sessionRepo,
		jwtManager:      jwtManager,
		passwordManager: passwordManager,
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error) {
	// 根据用户名或邮箱查找用户
	var user *model.User
	var err error

	if isEmail(req.Username) {
		user, err = s.userRepo.GetByEmail(ctx, req.Username)
	} else {
		user, err = s.userRepo.GetByUsername(ctx, req.Username)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	// 检查用户状态
	if user.Status != model.UserStatusActive {
		return nil, fmt.Errorf("user account is not active")
	}

	// 验证密码
	valid, err := s.passwordManager.VerifyPassword(req.Password, user.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to verify password: %w", err)
	}

	if !valid {
		return nil, fmt.Errorf("invalid password")
	}

	// 获取用户的默认租户
	tenantID, err := s.getDefaultTenantID(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get default tenant: %w", err)
	}

	// 生成令牌
	accessToken, err := s.jwtManager.GenerateToken(user, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := s.jwtManager.GenerateRefreshToken(user, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// 保存会话
	session := &model.UserSession{
		BaseModel: model.BaseModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:    user.ID,
		Token:     accessToken,
		ExpiresAt: time.Now().Add(24 * time.Hour), // TODO: 从配置中获取
	}

	if err := s.sessionRepo.Create(ctx, session); err != nil {
		logger.Warnf("Failed to save session: %v", err)
	}

	// 更新最后登录时间
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID); err != nil {
		logger.Warnf("Failed to update last login: %v", err)
	}

	// 构建响应
	response := &model.LoginResponse{
		Token:        accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    session.ExpiresAt,
		User: model.UserInfo{
			ID:          user.ID,
			Username:    user.Username,
			Email:       user.Email,
			DisplayName: user.DisplayName,
			Avatar:      user.Avatar,
			Role:        user.Role,
			Status:      user.Status,
		},
	}

	logger.Infof("User %s logged in successfully", user.Username)
	return response, nil
}

// RefreshToken 刷新令牌
func (s *authService) RefreshToken(ctx context.Context, refreshToken string) (*model.LoginResponse, error) {
	accessToken, newRefreshToken, err := s.jwtManager.RefreshToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}

	// 从令牌中获取用户信息
	claims, err := s.jwtManager.ValidateToken(accessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to validate new token: %w", err)
	}

	// 获取用户信息
	user, err := s.userRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	// 构建响应
	response := &model.LoginResponse{
		Token:        accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    time.Now().Add(24 * time.Hour), // TODO: 从配置中获取
		User: model.UserInfo{
			ID:          user.ID,
			Username:    user.Username,
			Email:       user.Email,
			DisplayName: user.DisplayName,
			Avatar:      user.Avatar,
			Role:        user.Role,
			Status:      user.Status,
		},
	}

	return response, nil
}

// Logout 用户登出
func (s *authService) Logout(ctx context.Context, userID uuid.UUID, token string) error {
	// 删除会话
	if err := s.sessionRepo.DeleteByToken(ctx, token); err != nil {
		logger.Warnf("Failed to delete session: %v", err)
	}

	logger.Infof("User %s logged out", userID)
	return nil
}

// ChangePassword 修改密码
func (s *authService) ChangePassword(ctx context.Context, userID uuid.UUID, req *model.ChangePasswordRequest) error {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if user == nil {
		return fmt.Errorf("user not found")
	}

	// 验证旧密码
	valid, err := s.passwordManager.VerifyPassword(req.OldPassword, user.Password)
	if err != nil {
		return fmt.Errorf("failed to verify old password: %w", err)
	}

	if !valid {
		return fmt.Errorf("invalid old password")
	}

	// 验证新密码强度
	if err := s.passwordManager.ValidatePasswordStrength(req.NewPassword); err != nil {
		return fmt.Errorf("password strength validation failed: %w", err)
	}

	// 哈希新密码
	hashedPassword, err := s.passwordManager.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// 更新密码
	user.Password = hashedPassword
	user.UpdatedAt = time.Now()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	logger.Infof("User %s changed password", user.Username)
	return nil
}

// CreateUser 创建用户
func (s *authService) CreateUser(ctx context.Context, req *model.CreateUserRequest) (*model.User, error) {
	// 检查用户名是否已存在
	existingUser, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, fmt.Errorf("failed to check username: %w", err)
	}
	if existingUser != nil {
		return nil, fmt.Errorf("username already exists")
	}

	// 检查邮箱是否已存在
	existingUser, err = s.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check email: %w", err)
	}
	if existingUser != nil {
		return nil, fmt.Errorf("email already exists")
	}

	// 验证密码强度
	if err := s.passwordManager.ValidatePasswordStrength(req.Password); err != nil {
		return nil, fmt.Errorf("password strength validation failed: %w", err)
	}

	// 哈希密码
	hashedPassword, err := s.passwordManager.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 创建用户
	now := time.Now()
	user := &model.User{
		BaseModel: model.BaseModel{
			ID:        uuid.New(),
			CreatedAt: now,
			UpdatedAt: now,
		},
		Username:    req.Username,
		Email:       req.Email,
		Password:    hashedPassword,
		DisplayName: req.DisplayName,
		Phone:       req.Phone,
		Role:        req.Role,
		Status:      model.UserStatusActive,
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	logger.Infof("User %s created successfully", user.Username)
	return user, nil
}

// UpdateUser 更新用户
func (s *authService) UpdateUser(ctx context.Context, id uuid.UUID, req *model.UpdateUserRequest) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	// 更新字段
	if req.DisplayName != "" {
		user.DisplayName = req.DisplayName
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Role != "" {
		user.Role = req.Role
	}
	if req.Status != "" {
		user.Status = req.Status
	}

	user.UpdatedAt = time.Now()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	logger.Infof("User %s updated successfully", user.Username)
	return user, nil
}

// GetUser 获取用户
func (s *authService) GetUser(ctx context.Context, id uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	return user, nil
}

// ListUsers 列出用户
func (s *authService) ListUsers(ctx context.Context, page, pageSize int) (*model.ListResponse, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	offset := (page - 1) * pageSize
	users, total, err := s.userRepo.List(ctx, pageSize, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	totalPage := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPage++
	}

	return &model.ListResponse{
		Data: users,
		Pagination: model.PaginationResponse{
			Page:      page,
			PageSize:  pageSize,
			Total:     total,
			TotalPage: totalPage,
		},
	}, nil
}

// getDefaultTenantID 获取用户的默认租户ID
func (s *authService) getDefaultTenantID(ctx context.Context, userID uuid.UUID) (uuid.UUID, error) {
	// TODO: 实现获取用户默认租户的逻辑
	// 这里暂时返回一个固定的UUID
	return uuid.New(), nil
}

// isEmail 检查字符串是否为邮箱格式
func isEmail(str string) bool {
	// 简单的邮箱格式检查
	return len(str) > 0 && strings.Contains(str, "@")
}
